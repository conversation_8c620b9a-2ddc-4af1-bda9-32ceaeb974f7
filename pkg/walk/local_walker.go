package walk

import (
	"context"
	"github.com/charlievieth/fastwalk"
	"go.uber.org/zap"
	"io/fs"
)

// NewWalker 创建文件遍历器
func NewWalker(config Walker<PERSON>onfig, logger *zap.Logger) Walker {
	return &localWalker{
		config: config,
		logger: logger,
	}
}

// localWalker 本地文件系统遍历器
type localWalker struct {
	config WalkerConfig
	logger *zap.Logger
}

func (w *localWalker) Walk(ctx context.Context, root string, handler <PERSON><PERSON>) error {
	realWalkFunc := func(pathName string, d fs.DirEntry, err error) error {
		if err != nil {
			w.logger.Error("walk error ", zap.Error(err))
			return err
		}
		info, err := d.Info()
		if err != nil || info == nil {
			w.logger.Error("info error", zap.Error(err))
			return err
		}
		return handler(err, FileInfo{
			PathName: pathName,
			Size:     info.Size(),
			IsDir:    d.<PERSON>ir(),
		})
	}
	if err := fastwalk.Walk(&fastwalk.Config{NumWorkers: w.config.Concurrency}, // 限制并发数
		root, realWalkFunc); err != nil {
		return err
	}
	return nil

}

func (w *localWalker) Close() error {
	return nil
}

func (w *sftpWalker) Close() error {
	w.pool.Close()
	return nil
}

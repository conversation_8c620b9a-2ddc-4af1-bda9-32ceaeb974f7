package walk

import (
	"context"
	"fmt"
	"path"
	"sync"
	"sync/atomic"
	"time"

	"git.woa.com/msp/toolbox/cfs-migrate-tool/pkg/utils"
	"go.uber.org/zap"
)

// sftpWalker SFTP文件系统遍历器
type sftpWalker struct {
	config WalkerConfig
	pool   *utils.SFTPConnectionPool
	logger *zap.Logger
}

func NewSFTPWalker(config WalkerConfig, pool *utils.SFTPConnectionPool, logger *zap.Logger) Walker {
	return &sftpWalker{
		config: config,
		pool:   pool,

		logger: logger,
	}
}

type SftpWalkerConfig struct {
	wg           sync.WaitGroup
	pending      int64 // 使用原子操作计数
	dirQueue     chan string
	done         chan struct{}
	drain        chan struct{}
	dirsScanned  int64 // 统计已扫描目录数，从1开始计数(包含根目录)
	filesScanned int64 // 统计已扫描文件数
	startTime    time.Time
	statInterval time.Duration
}

func (w *sftpWalker) Walk(ctx context.Context, root string, handler <PERSON><PERSON><PERSON><PERSON>) error {

	var sftpWalkerConfig SftpWalkerConfig = SftpWalkerConfig{
		wg:           sync.WaitGroup{},
		dirQueue:     make(chan string, 50000),
		done:         make(chan struct{}),
		drain:        make(chan struct{}),
		dirsScanned:  int64(1),
		filesScanned: int64(0),
		startTime:    time.Now(),
		statInterval: 5 * time.Second,
	}

	//定期打印性能数据
	go func() {
		ticker := time.NewTicker(sftpWalkerConfig.statInterval)
		defer ticker.Stop()

		for {
			select {
			case <-ctx.Done():
				return
			case <-sftpWalkerConfig.done:
				return
			case <-sftpWalkerConfig.drain:
				close(sftpWalkerConfig.done)
				return
			case <-ticker.C:
				elapsed := time.Since(sftpWalkerConfig.startTime).Seconds()
				dirCount := atomic.LoadInt64(&sftpWalkerConfig.dirsScanned)
				fileCount := atomic.LoadInt64(&sftpWalkerConfig.filesScanned)
				totalCount := dirCount + fileCount

				fmt.Printf("Status Report - Directory: %d (%.2f/s), File: %d (%.2f/s), Total entries: %d, Runtime:%.1fs\n",
					dirCount, float64(dirCount)/elapsed,
					fileCount, float64(fileCount)/elapsed,
					totalCount,
					elapsed)

				pendingCount := atomic.LoadInt64(&sftpWalkerConfig.pending)
				fmt.Printf("Pending: %d, Directory queue: %d\n",
					pendingCount, len(sftpWalkerConfig.dirQueue))

				// 检查是否工作已完成但没有触发关闭信号
				if pendingCount == 0 && len(sftpWalkerConfig.dirQueue) == 0 {
					select {
					case <-sftpWalkerConfig.done: // 已关闭
					default: // 尚未关闭
						close(sftpWalkerConfig.done)
						return
					}
				}
			}
		}
	}()

	// 启动目录扫描器current
	for i := 0; i < w.config.Concurrency; i++ {
		sftpWalkerConfig.wg.Add(1)
		go func() {
			defer func() {
				sftpWalkerConfig.wg.Done()
			}()
			for {
				select {
				case <-ctx.Done():
					return
				case <-sftpWalkerConfig.done:
					return
				case dirPath, ok := <-sftpWalkerConfig.dirQueue:
					if !ok {
						return
					}
					done, err := w.doQuickWalk(handler, dirPath, &sftpWalkerConfig)
					if err != nil {
						w.logger.Error("Failed to walk directory",
							zap.String("path", dirPath),
							zap.Error(err))
					}
					if done {
						return
					}
				}
			}
		}()
	}

	// 初始化遍历
	atomic.AddInt64(&sftpWalkerConfig.pending, 1)
	sftpWalkerConfig.dirQueue <- root

	// 等待所有goroutine完成
	<-sftpWalkerConfig.done
	close(sftpWalkerConfig.dirQueue)
	sftpWalkerConfig.wg.Wait()

	// 输出统计信息
	totalEntries := sftpWalkerConfig.dirsScanned + sftpWalkerConfig.filesScanned
	w.logger.Info("Scan completed",
		zap.Int64("directories", sftpWalkerConfig.dirsScanned),
		zap.Int64("files", sftpWalkerConfig.filesScanned),
		zap.Int64("total", totalEntries),
		zap.Duration("duration", time.Since(sftpWalkerConfig.startTime)))

	return nil
}

func (w *sftpWalker) doQuickWalk(handler WalkerHandler, dirPath string, sftpWalkerConfig *SftpWalkerConfig) (done bool, err error) {
	// 在处理目录时获取连接
	var client *utils.SFTPClient
	for retries := 0; retries < 3; retries++ {
		client, err = w.pool.GetClient()
		if err == nil {
			break
		}
		w.logger.Error("Failed to obtain SFTP connection, retrying...",
			zap.Int("retry", retries+1),
			zap.Error(err))
		time.Sleep(time.Second * time.Duration(retries+1))
	}
	if err != nil {
		w.logger.Error("Failed to obtain SFTP connection, giving up",
			zap.Error(err))
		_ = handler(err, FileInfo{
			PathName: dirPath,
			Size:     0,
			IsDir:    true,
		})
		atomic.AddInt64(&sftpWalkerConfig.pending, -1)
		return false, err
	}
	defer func() {
		w.pool.ReleaseClient(client)
	}()

	// 处理目录
	files, err := client.ReadDir(dirPath)
	if err != nil {
		_ = handler(err, FileInfo{
			PathName: dirPath,
			Size:     0,
			IsDir:    true,
		})
		w.logger.Error("Failed to read directory",
			zap.String("path", dirPath),
			zap.Error(err))
		atomic.AddInt64(&sftpWalkerConfig.pending, -1)
		return false, err
	}

	// 处理文件和子目录
	fileCount := 0
	dirCount := 0
	for _, f := range files {
		if f.IsDir() {
			dirCount++
		} else {
			fileCount++
		}
	}
	atomic.AddInt64(&sftpWalkerConfig.filesScanned, int64(fileCount))
	// 处理文件和添加子目录到队列
	for _, f := range files {
		fullPath := path.Join(dirPath, f.Name())
		if f.IsDir() {
			atomic.AddInt64(&sftpWalkerConfig.pending, 1)
			atomic.AddInt64(&sftpWalkerConfig.dirsScanned, 1)
			select {
			case sftpWalkerConfig.dirQueue <- fullPath:
			case <-sftpWalkerConfig.done:
				return true, nil
			}
		}
		_ = handler(nil, FileInfo{
			PathName: fullPath,
			Size:     f.Size(),
			IsDir:    f.IsDir(),
		})
	}

	// 更新计数器
	if atomic.AddInt64(&sftpWalkerConfig.pending, -1) == 0 {
		select {
		case sftpWalkerConfig.drain <- struct{}{}:
		default:
		}
	}
	return false, nil
}

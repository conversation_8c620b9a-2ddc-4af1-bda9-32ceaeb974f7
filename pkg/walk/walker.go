package walk

import (
	"context"
	"os"
	"runtime"
	"time"
)

// FileHandler 文件处理回调函数类型
type FileHandler func(path string, info os.FileInfo) error

// FileInfo 文件信息结构
type FileInfo struct {
	PathName string // 文件全路径
	Size     int64  // 文件大小
	IsDir    bool   // 是否为目录
}

// WalkerHandler 文件处理回调函数类型
type WalkerHandler func(err error, info FileInfo) error

// Walker 文件遍历器接口
type Walker interface {
	// Walk 遍历指定路径下的所有文件和目录
	Walk(ctx context.Context, root string, handler WalkerHandler) error
	// Close 关闭遍历器，释放资源
	Close() error
}

// WalkerConfig 遍历器配置
type WalkerConfig struct {
	Concurrency int           // 并发数
	Timeout     time.Duration // 超时时间
	SrcDir      string        // 源目录
}

// DefaultWalkerConfig 默认配置
var DefaultWalkerConfig = WalkerConfig{
	Concurrency: 2 * runtime.NumCPU(),
	Timeout:     30 * time.Second,
}

const (
	Local = "local"
	SSH   = "ssh"
)

package check

import (
	"bufio"
	"context"
	"crypto/md5"
	"encoding/hex"
	"errors"
	"fmt"
	"git.woa.com/msp/toolbox/cfs-migrate-tool/pkg/common"
	"git.woa.com/msp/toolbox/cfs-migrate-tool/pkg/utils"
	"git.woa.com/msp/toolbox/cfs-migrate-tool/pkg/walk"
	"github.com/panjf2000/ants/v2"
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
	"io"
	"net/http"
	_ "net/http/pprof"
	"os"
	"path"
	"path/filepath"
	"runtime"
	"strconv"
	"strings"
	"sync"
	"sync/atomic"
	"time"
)

// FileInfo 文件信息结构
type FileInfo struct {
	Path   string // 文件路径
	Size   int64  // 文件大小
	Hash   string // 文件 MD5 哈希值（可选）
	SrcErr bool   // 文件读取错误标志
}

// Task 任务结构
type Task struct {
	Files []*FileInfo // 文件列表
}

// Config 配置参数
type Config struct {
	SrcDir        string // 源目录
	DstDir        string // 目标目录
	Threshold     int    // 文件数量阈值
	CheckMD5      bool   // 是否进行 MD5 校验
	CheckParallel int    // 并发校验数量
	Verbose       bool
	ScanOnly      bool
	FromFile      string
	StartTime     string
	EndTime       string
	EnablePprof   bool
	RemoteConfig  RemoteConfig
}

type RemoteConfig struct {
	RemoteIp        string
	RemotePort      string
	RemoterUserName string
	RemotePassword  string
}

type ScanAndCheckTask struct {
	wg            sync.WaitGroup
	taskChannel   chan *Task
	semaphore     chan struct{}
	hashSemaphore chan struct{}
	// scanCount 2^63-1 8PB个数量会溢出？  暂不考虑！
	scanCount int64
	scanSize  uint64
	// 同上
	checkSize     uint64
	checkCount    int64
	targetSize    uint64
	targetCount   int64
	notEqualCount int64
	writeLock     sync.Mutex
	config        *Config
	scanDone      bool
	logger        *zap.Logger
	remoteConfig  RemoteConfig
}

func (s *ScanAndCheckTask) GetProgress() common.ProgressInfo {

	return common.ProgressInfo{
		ScanCount:       s.scanCount,
		CheckCount:      s.checkCount,
		ScanSize:        s.scanSize,
		CheckSize:       s.checkSize,
		ScanOrCheckDone: s.scanDone,
		NotEqualCount:   s.notEqualCount,
		TargetSize:      s.targetSize,
		TargetCount:     s.targetCount,
	}
}

func NewScanOrCheckTask(config *Config) *ScanAndCheckTask {

	cfg := zap.NewProductionConfig()
	cfg.OutputPaths = []string{"logs/check_log.log"}
	cfg.EncoderConfig.EncodeTime = func(t time.Time, enc zapcore.PrimitiveArrayEncoder) {
		enc.AppendString(t.Format("2006-01-02 15:04:05.000"))
	}
	log, err := cfg.Build()
	fmt.Printf("logger: %v, err: %v", log, err)

	return &ScanAndCheckTask{
		taskChannel:   make(chan *Task, 1000),
		semaphore:     make(chan struct{}, config.CheckParallel),
		hashSemaphore: make(chan struct{}, config.CheckParallel),
		scanCount:     0,
		checkCount:    0,
		notEqualCount: 0,
		config:        config,
		logger:        log,
		remoteConfig:  config.RemoteConfig,
	}
}
func (s *ScanAndCheckTask) ScanAndCheck() {

	// 参数校验 ， 工单
	if s.remoteConfig.RemoteIp != "" && s.remoteConfig.RemotePort != "" && s.remoteConfig.RemoterUserName != "" {
		err := utils.ValidateRemoteConfig(s.remoteConfig.RemoteIp, s.remoteConfig.RemoterUserName, s.remoteConfig.RemotePassword, s.remoteConfig.RemotePort)
		if err != nil {
			s.logger.Error("failed to validate remote config", zap.Error(err))
			return
		}
	}

	if s.config.EnablePprof {
		// 启动 pprof HTTP 服务器
		go func() {
			http.ListenAndServe("0.0.0.0:6060", nil)
		}()
	}

	// 初始化配置
	scanStartTime := time.Now() // 获取当前时间
	// 初始化 Channel 和信号量   背压 ,避免worker处理不过来，大量元数据提前加载到内存中
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	utils.DeleteFile("logs/notEqual.txt")
	utils.DeleteFile("logs/all_check.txt")

	if s.config.ScanOnly {
		utils.TruncateFile("logs/scan.txt")
	}

	// 启动任务监听 Goroutine
	go processTasks(ctx, s.config, s)
	go func() {
		timer := time.NewTicker(time.Second)
		for {
			select {
			case <-timer.C:
				scanElapsed := time.Since(scanStartTime).Seconds()
				s.logger.Info("scanAndCheckTask", zap.Int64("scanTaskCount", s.scanCount),
					zap.Int64("checkTaskCount", s.checkCount), zap.Int64("notEqualCount", s.notEqualCount),
					zap.Float64("scanElapsed", scanElapsed))

			case <-ctx.Done():
				s.logger.Info("scanAndCheckTask done")

				return
			}
		}
	}()

	// 遍历源目录并生成任务
	err := s.QuickWalkDirOrReadFromFile()
	if err != nil {
		s.logger.Error("walkDir err", zap.Error(err))
		return
	}

	// 关闭 Channel 并等待所有任务完成
	close(s.taskChannel)
	s.wg.Wait()
	scanElapsed := time.Since(scanStartTime).Seconds()
	s.logger.Info("scanAndCheckTask done", zap.Int64("scanTaskCount", s.scanCount),
		zap.Int64("checkTaskCount", s.checkCount), zap.Int64("notEqualCount", s.notEqualCount),
		zap.Float64("scanElapsed", scanElapsed))
	s.scanDone = true
}

func (s *ScanAndCheckTask) QuickWalkDirOrReadFromFile() error {

	currentMap := NewConcurrentMap(s.config.CheckParallel)

	isRemote := len(strings.TrimSpace(s.config.RemoteConfig.RemoteIp)) > 0 && len(strings.TrimSpace(s.config.RemoteConfig.RemotePort)) > 0

	if len(s.config.FromFile) > 0 {
		// 打开文件
		file, err := os.Open(s.config.FromFile)
		if err != nil {
			s.logger.Error("Failed to open file", zap.Error(err))
			return err
		}
		defer file.Close() // 确保文件关闭

		// 创建一个 Scanner 来逐行读取文件
		scanner := bufio.NewScanner(file)
		// 检查是否有错误发生
		if err := scanner.Err(); err != nil {
			s.logger.Error("Error while reading file", zap.Error(err))
		}

		client := &utils.SFTPClient{}
		if isRemote {
			client, err = utils.NewSFTPClientByUserPass(s.config.RemoteConfig.RemoterUserName, s.config.RemoteConfig.RemotePassword, s.config.RemoteConfig.RemoteIp, s.config.RemoteConfig.RemotePort)
			if err != nil {
				s.logger.Error("Error while creating SFTP client", zap.Error(err))
				return err
			}
			defer client.Close()
		}
		// 逐行读取
		for scanner.Scan() {
			line := scanner.Text() // 获取当前行的内容

			newLine := path.Join(s.config.SrcDir, line)
			param := strings.Split(newLine, "|")
			if len(param) == 2 {
				size, err := strconv.ParseInt(param[1], 10, 64) // 字符串转 int
				if err != nil {
					s.logger.Error("Failed to parse size", zap.Error(err))
					continue
				}
				currentMap.appendFile(param[0], size, s.config.Threshold, false, s)
			}
			if len(param) == 1 {
				var stat os.FileInfo
				var err_ error
				if isRemote {
					stat, err_ = client.Stat(param[0])
				} else {
					stat, err_ = os.Stat(param[0])
				}
				if err_ != nil {
					s.logger.Error("Failed to get file info", zap.Error(err_))
					currentMap.appendFile(param[0], 0, s.config.Threshold, true, s)
					continue
				}
				currentMap.appendFile(param[0], stat.Size(), s.config.Threshold, false, s)
				continue
			}
		}
		currentMap.flushRemainingFiles(s)
		return nil
	}

	walkFn := func(err error, info walk.FileInfo) error {
		if err != nil {
			s.logger.Error(" func Error walking directory", zap.Error(err))
			return err
		}
		if !info.IsDir {
			currentMap.appendFile(info.PathName, info.Size, s.config.Threshold, false, s)
		}
		return nil
	}
	var walker walk.Walker

	if isRemote {
		pool, err := utils.NewSFTPConnectionPool(utils.NewClientConfig(s.config.RemoteConfig.RemoterUserName, s.config.RemoteConfig.RemotePassword),
			fmt.Sprintf("%s:%s", s.config.RemoteConfig.RemoteIp, s.config.RemoteConfig.RemotePort), runtime.NumCPU())
		if err != nil {
			s.logger.Error("Error creating SFTP connection pool", zap.Error(err))
			return err
		}

		walker = walk.NewSFTPWalker(walk.WalkerConfig{
			Concurrency: 5 * runtime.NumCPU(),
			SrcDir:      s.config.SrcDir,
		}, pool, s.logger)
	} else {
		walker = walk.NewWalker(walk.WalkerConfig{
			Concurrency: s.config.CheckParallel,
			SrcDir:      s.config.SrcDir,
		}, s.logger)
	}

	err := walker.Walk(context.Background(), s.config.SrcDir, walkFn)
	if err != nil {
		s.logger.Error("Error walking directory", zap.String("directory", s.config.SrcDir), zap.Error(err))
		return err
	}
	// 处理剩余的一批文件
	currentMap.flushRemainingFiles(s)
	s.logger.Info("walkDir done")
	return nil
}

// processTasks 监听任务 Channel 并处理任务
func processTasks(ctx context.Context, config *Config, scanAndCheckTask *ScanAndCheckTask) {

	pool, _ := ants.NewPool(1000, ants.WithPreAlloc(true))
	pool.Waiting()

	defer func() {
		pool.Release()
	}()

	scanAndCheckTask.wg.Add(config.CheckParallel)
	// 动态根据内存 、cpu负载调整并发数
	for i := 0; i < config.CheckParallel; i++ {
		_ = pool.Submit(func() {
			defer scanAndCheckTask.wg.Done()
			for {
				select {
				case task, ok := <-scanAndCheckTask.taskChannel:
					if !ok {
						scanAndCheckTask.logger.Info("Task channel closed")
						return
					}
					result, _ := scanOrCompareFiles(task, config, scanAndCheckTask)
					//  扩展动作， 比如写入文件或者写入到其他目标端存储
					scanAndCheckTask.writeLock.Lock()
					if config.ScanOnly {

						err := utils.WriteStringsToFile("logs/scan.txt", convertFileListToStrArray(result.getAllEqual()), "")
						if err != nil {
							scanAndCheckTask.logger.Error("write scan.txt err", zap.Error(err))
						}

					} else {
						err := utils.WriteStringsToFile("logs/notEqual.txt", convertFileListToStrArray(result.getAllNotEqual()), "")
						if err != nil {
							scanAndCheckTask.logger.Error("write notEqual.txt err", zap.Error(err))
						}

						if config.Verbose == true {
							err := utils.WriteStringsToFile("logs/all_check.txt", convertFileListToStrArray(result.getAllCheck()), "")
							if err != nil {
								scanAndCheckTask.logger.Error("write all_check.txt err", zap.Error(err))
							}
						}
					}
					scanAndCheckTask.writeLock.Unlock()
				case <-ctx.Done(): // 监听取消信号（可选）
					scanAndCheckTask.logger.Info("Task processing canceled")
					return
				}
			}
		})
	}
}

// 补充参数做是否需要打印更多信息
func convertFileListToStrArray(files []*FileInfo) []string {
	var strArray []string
	for _, srcFile := range files {
		strArray = append(strArray, fmt.Sprintf("%s|%d", srcFile.Path, srcFile.Size))
	}
	return strArray
}

type FilesCheckResult struct {
	SizeNotEqual []*FileInfo
	MD5NotEqual  []*FileInfo
	NotExists    []*FileInfo
	CanNotCheck  []*FileInfo
	AllEqual     []*FileInfo
}

func (f FilesCheckResult) getAllNotEqual() []*FileInfo {
	notEqual := make([]*FileInfo, 0)
	notEqual = append(notEqual, f.SizeNotEqual...)
	notEqual = append(notEqual, f.MD5NotEqual...)
	notEqual = append(notEqual, f.NotExists...)
	notEqual = append(notEqual, f.CanNotCheck...)
	return notEqual
}

func (f FilesCheckResult) getAllEqual() []*FileInfo {
	equal := make([]*FileInfo, 0)
	equal = append(equal, f.AllEqual...)
	return equal
}
func (f FilesCheckResult) getAllCheck() []*FileInfo {
	check := make([]*FileInfo, 0)
	check = append(check, f.getAllEqual()...)
	check = append(check, f.getAllNotEqual()...)
	return check
}

// compareFiles 对比文件元数据和 MD5
func scanOrCompareFiles(task *Task, config *Config, scanAndCheckTask *ScanAndCheckTask) (FilesCheckResult, error) {
	result := FilesCheckResult{}
	var needHashDstPaths []string
	var needHashSrcPaths []string

	if config.ScanOnly {
		for _, srcFile := range task.Files {
			result.AllEqual = append(result.AllEqual, srcFile)
		}
		return result, nil
	}

	// 对比目标文件
	for _, srcFile := range task.Files {
		atomic.AddInt64(&scanAndCheckTask.checkCount, 1)
		atomic.AddUint64(&scanAndCheckTask.checkSize, uint64(srcFile.Size))

		if srcFile.SrcErr {
			result.CanNotCheck = append(result.CanNotCheck, srcFile)
			atomic.AddInt64(&scanAndCheckTask.notEqualCount, 1)
			continue
		}

		dstPath := filepath.Join(config.DstDir, srcFile.Path[len(config.SrcDir):])
		dstFile, err := os.Stat(dstPath)
		if err != nil {
			if errors.Is(err, os.ErrNotExist) {
				result.NotExists = append(result.NotExists, srcFile)
			} else {
				result.CanNotCheck = append(result.CanNotCheck, srcFile)
			}
			atomic.AddInt64(&scanAndCheckTask.notEqualCount, 1)
			continue
		}
		atomic.AddInt64(&scanAndCheckTask.targetCount, 1)
		atomic.AddUint64(&scanAndCheckTask.targetSize, uint64(dstFile.Size()))
		// 提供对其他条件的扩展
		// 对比文件大小
		if srcFile.Size != dstFile.Size() {
			result.SizeNotEqual = append(result.SizeNotEqual, srcFile)
			atomic.AddInt64(&scanAndCheckTask.notEqualCount, 1)
			continue
		}
		needHashSrcPaths = append(needHashSrcPaths, srcFile.Path)
		needHashDstPaths = append(needHashDstPaths, dstPath)

		if config.CheckMD5 {
			srcHash := ""
			dstHash := ""
			wg := &sync.WaitGroup{}
			wg.Add(2)
			go func() {
				defer wg.Done()
				srcHash, err = calculateMD5(srcFile.Path)
				if err != nil {
					scanAndCheckTask.logger.Error("calculateMD5 err", zap.Error(err))
				}
			}()
			go func() {
				defer wg.Done()
				dstHash, err = calculateMD5(dstPath)
				if err != nil {
					scanAndCheckTask.logger.Error("calculateMD5 err", zap.Error(err))
				}
			}()
			wg.Wait()
			if srcHash != dstHash {
				result.MD5NotEqual = append(result.MD5NotEqual, srcFile)
				atomic.AddInt64(&scanAndCheckTask.notEqualCount, 1)
				continue
			}
		}
		result.AllEqual = append(result.AllEqual, srcFile)
	}
	return result, nil
}

// batchCalculateMD5 批量计算文件的 MD5 哈希值
func batchCalculateMD5(files []string, scanAndCheckTask *ScanAndCheckTask) map[string]string {
	var wg sync.WaitGroup
	hashMap := make(map[string]string)
	hashChan := make(chan struct {
		Path string
		Hash string
	}, len(files))

	// 启动多个 Goroutine 并发计算 MD5
	for _, file := range files {
		wg.Add(1)
		scanAndCheckTask.hashSemaphore <- struct{}{}
		go func(f string) {
			defer wg.Done()

			defer func() { <-scanAndCheckTask.hashSemaphore }()
			hash, err := calculateMD5(f)
			if err != nil {
				scanAndCheckTask.logger.Error("calculateMD5 err", zap.Error(err))
				return
			}
			hashChan <- struct {
				Path string
				Hash string
			}{Path: f, Hash: hash}
		}(file)
	}

	// 等待所有 Goroutine 完成
	wg.Wait()
	close(hashChan)

	// 将结果写入映射
	for result := range hashChan {
		hashMap[result.Path] = result.Hash
	}

	return hashMap
}

// calculateMD5 计算文件的 MD5 哈希值
func calculateMD5(path string) (string, error) {
	file, err := os.Open(path)
	if err != nil {
		return "", err
	}
	defer file.Close()

	hash := md5.New()
	if _, err := io.Copy(hash, file); err != nil {
		return "", err
	}
	return hex.EncodeToString(hash.Sum(nil)), nil
}

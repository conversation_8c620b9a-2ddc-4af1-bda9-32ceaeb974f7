package check

import (
	"sync"
	"sync/atomic"
)

type Shard struct {
	mu    sync.Mutex
	files []*FileInfo
}

type ConcurrentMap struct {
	shards []*Shard
}

func NewConcurrentMap(numShards int) *ConcurrentMap {
	shards := make([]*Shard, numShards)
	for i := 0; i < numShards; i++ {
		shards[i] = &Shard{files: make([]*FileInfo, 0)}
	}
	return &ConcurrentMap{shards: shards}
}

func (m *ConcurrentMap) getShard(path string) *Shard {
	hash := fnv32(path)
	return m.shards[hash%uint32(len(m.shards))]
}

func (m *ConcurrentMap) appendFile(path string, size int64, threshold int, srcReadErr bool, scanAndCheckTask *ScanAndCheckTask) {
	shard := m.getShard(path)
	shard.mu.Lock()
	defer shard.mu.Unlock()

	shard.files = append(shard.files, &FileInfo{
		Path:   path,
		Size:   size,
		SrcErr: srcReadErr,
	})
	atomic.AddInt64(&scanAndCheckTask.scanCount, 1)
	atomic.AddUint64(&scanAndCheckTask.scanSize, uint64(size))
	// 如果文件数量达到阈值，生成任务并投递到 channel
	if len(shard.files) >= threshold {
		scanAndCheckTask.taskChannel <- &Task{Files: shard.files}
		shard.files = []*FileInfo{} // 重置分片中的文件列表
	}
}

// 处理剩余的文件
func (m *ConcurrentMap) flushRemainingFiles(scanAndCheckTask *ScanAndCheckTask) {
	for _, shard := range m.shards {
		shard.mu.Lock()
		if len(shard.files) > 0 {
			scanAndCheckTask.taskChannel <- &Task{Files: shard.files}
			shard.files = []*FileInfo{} // 重置分片中的文件列表
		}
		shard.mu.Unlock()
	}
}

func fnv32(key string) uint32 {
	hash := uint32(2166136261)
	const prime32 = uint32(16777619)
	for i := 0; i < len(key); i++ {
		hash *= prime32
		hash ^= uint32(key[i])
	}
	return hash
}

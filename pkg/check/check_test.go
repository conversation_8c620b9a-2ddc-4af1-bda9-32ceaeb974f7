package check

import (
	"fmt"
	"github.com/charlievieth/fastwalk"
	"go.uber.org/zap"
	"io/fs"
	"path/filepath"
	"reflect"
	"sync"
	"testing"
	"time"
)

func TestPathChange(t *testing.T) {

	src := "/Users/<USER>/Documents/code/GolandProjects/cfs-migrate-tool/cfs-migrate-tool/pkg/check/check.go"

	dstPath := filepath.Join("/Users/<USER>/Documents1/", src[len("/Users/<USER>/Documents"):])
	t.Log(dstPath)
}

func TestAA(t *testing.T) {
	taskChannel := make(chan int, 10) // 有缓冲的 channel
	wg := &sync.WaitGroup{}
	wg.Add(3)
	// 生产者
	go func() {
		defer wg.Done()
		for i := 0; i < 10; i++ {
			taskChannel <- i
			fmt.Printf("Produced: %d\n", i)
		}
		close(taskChannel) // 生产完成后关闭 channel
		fmt.Println("Producer closed the channel")
	}()

	// 消费者
	go func() {
		defer wg.Done()
		time.Sleep(2 * time.Second) // 等待生产者生产完毕
		for {
			task, ok := <-taskChannel
			if !ok {
				fmt.Println("Consumer1 received close signal")
				break
			}
			fmt.Printf("Consumed1: %d\n", task)
			time.Sleep(100 * time.Millisecond) // 模拟消费耗时
		}
	}()
	// 消费者
	go func() {
		defer wg.Done()
		time.Sleep(2 * time.Second) // 等待生产者生产完毕
		for {
			task, ok := <-taskChannel
			if !ok {
				fmt.Println("Consumer2 received close signal")
				break
			}
			fmt.Printf("Consumed2: %d\n", task)
			time.Sleep(100 * time.Millisecond) // 模拟消费耗时
		}
	}()

	wg.Wait()
	t.Log("done")

}

func TestFastWalk(t *testing.T) {
	walkFn := func(pathname string, de fs.DirEntry, err error) error {
		if err != nil {
			fmt.Printf("Error walking directory: %s, %v\n", pathname, err)
			return err
		}
		info, err := de.Info()
		print(info.Size())
		println(pathname)
		//if !de.IsDir() {
		//	//lock.Lock()
		//	//files = append(files, &FileInfo{
		//	//	PathName: pathname,
		//	//	Size: info.Size(),
		//	//})
		//	//
		//	//// 如果文件数量达到阈值，生成任务并投递到 channel
		//	//if len(files) >= threshold {
		//	//	scanCount++
		//	//	taskChannel <- &Task{Files: files}
		//	//	files = []*FileInfo{} // 重置 files 切片
		//	//}
		//	//lock.Unlock()
		//}
		//if de.IsDir() {
		//	info, err := de.Info()
		//	if err != nil {
		//
		//	}
		//	if info.Size() > 0 {
		//
		//	}
		//
		//}
		return nil
	}

	if err := fastwalk.Walk(&fastwalk.Config{NumWorkers: 4}, // 限制并发数
		"/Users/<USER>/Documents/code/GolandProjects/cfs-migrate-tool/cfs-migrate-tool/docs/", walkFn); err != nil {

	}
}

func TestScanAndCheckTask_ScanAndCheck(t *testing.T) {
	type fields struct {
		wg            sync.WaitGroup
		taskChannel   chan *Task
		semaphore     chan struct{}
		hashSemaphore chan struct{}
		scanCount     int64
		scanSize      uint64
		checkSize     uint64
		checkCount    int64
		notEqualCount int64
		writeLock     sync.Mutex
		config        *Config
		scanDone      bool
		logger        *zap.Logger
	}
	tests := []struct {
		name   string
		fields fields
	}{
		// TODO: Add test cases.
		{
			name: "test",
			fields: fields{
				wg:            sync.WaitGroup{},
				taskChannel:   make(chan *Task, 100),
				semaphore:     make(chan struct{}, 100),
				hashSemaphore: make(chan struct{}, 100),
				scanCount:     0,
				scanSize:      0,
				checkSize:     0,
				checkCount:    0,
				notEqualCount: 0,
				writeLock:     sync.Mutex{},
				config: &Config{
					SrcDir:    "/Users/<USER>/Documents/code/GolandProjects/cfs-migrate-tool/cfs-migrate-tool/docs/",
					DstDir:    "/Users/<USER>/Documents/code/GolandProjects/cfs-migrate-tool/cfs-migrate-tool/docs/",
					Threshold: 100,
				},
			},
		}}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &ScanAndCheckTask{
				wg:            tt.fields.wg,
				taskChannel:   tt.fields.taskChannel,
				semaphore:     tt.fields.semaphore,
				hashSemaphore: tt.fields.hashSemaphore,
				scanCount:     tt.fields.scanCount,
				scanSize:      tt.fields.scanSize,
				checkSize:     tt.fields.checkSize,
				checkCount:    tt.fields.checkCount,
				notEqualCount: tt.fields.notEqualCount,
				writeLock:     tt.fields.writeLock,
				config:        tt.fields.config,
				scanDone:      tt.fields.scanDone,
				logger:        tt.fields.logger,
			}
			s.ScanAndCheck()
		})
	}
}

func Test_batchCalculateMD51(t *testing.T) {
	type args struct {
		files            []string
		scanAndCheckTask *ScanAndCheckTask
	}
	tests := []struct {
		name string
		args args
		want map[string]string
	}{
		// : Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := batchCalculateMD5(tt.args.files, tt.args.scanAndCheckTask); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("batchCalculateMD5() = %v, want %v", got, tt.want)
			}
		})
	}
}

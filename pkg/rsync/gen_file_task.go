package rsync

import (
	"bufio"
	"context"
	"fmt"
	"git.woa.com/msp/toolbox/cfs-migrate-tool/pkg/utils"
	"git.woa.com/msp/toolbox/cfs-migrate-tool/pkg/walk"
	"go.uber.org/zap"
	"os"
	"path"
	"path/filepath"
	"runtime"
	"strconv"
	"strings"
	"sync"
	"sync/atomic"
)

var dirNodePool = sync.Pool{
	New: func() interface{} {
		return &dirNode{}
	},
}

func getDirNode(path string) *dirNode {
	node := dirNodePool.Get().(*dirNode)
	node.path = path
	node.hasFile = false
	node.processed = false
	node.parent = nil
	return node
}

type dirNode struct {
	path      string
	hasFile   bool
	processed bool
	parent    *dirNode
}

var (
	dirMap struct {
		sync.RWMutex
		m map[string]*dirNode
	}
)

// generateTasks 遍历目录， 拆分文件迁移任务
func (t *MigrateTask) generateTasks() {
	defer func() {
		if r := recover(); r != nil {
			t.logger.Error("Panic in generateTasks",
				zap.Any("recover", r), zap.Stack("stack"))
		}
	}()

	// 从文件清单模式
	if len(strings.TrimSpace(t.FromFileList)) > 0 {
		t.processFile()
		return

	}

	// 初始化
	fileTask := NewFileMigrateTask()
	dirMap.m = make(map[string]*dirNode)

	// 遍历文件 fn， fastwalk 会对每个文件都执行 fn
	walkFn := func(err error, info walk.FileInfo) error {
		// 增加 recover 处理
		defer func() {
			if r := recover(); r != nil {
				t.logger.Error("Panic in walkFn",
					zap.String("path", info.PathName),
					zap.Any("recover", r), zap.Stack("stack"))
			}
		}()
		if err != nil {
			if os.IsPermission(err) {
				t.logger.Error("File Permission denied", zap.String("path", info.PathName))
				errFile := ErrFileInfo{File: FileInfo{Path: info.PathName}, ErrMsg: err.Error()}
				t.errChan <- errFile
				return nil
			}
			if os.IsNotExist(err) {
				t.logger.Error("File not found", zap.String("path", info.PathName))
				errFile := ErrFileInfo{File: FileInfo{Path: info.PathName}, ErrMsg: err.Error()}
				t.errChan <- errFile
				return nil
			}
			t.logger.Error("Critical error walking path",
				zap.String("path", info.PathName),
				zap.Error(err))
			errFile := ErrFileInfo{File: FileInfo{Path: info.PathName}, ErrMsg: fmt.Errorf("critical error walking path: %w", err).Error()}
			t.errChan <- errFile
			return err // 返回错误以终止遍历
		}

		// 如果需要处理空目录才进行目录树构建(节省内存)
		if t.TaskConfig.ProcessEmptyDir {
			if info.IsDir {
				node := getDirNode(info.PathName)
				// 查找父节点
				parentPath := filepath.Dir(info.PathName)
				dirMap.RLock()
				if parent, ok := dirMap.m[parentPath]; ok {
					node.parent = parent
					dirMap.RUnlock()
				} else {
					dirMap.RUnlock()
				}

				// 存储当前节点
				dirMap.Lock()
				dirMap.m[info.PathName] = node
				dirMap.Unlock()
				return nil
			}

			// 标记父目录为非空
			parentDir := filepath.Dir(info.PathName)
			dirMap.RLock()
			if parent, ok := dirMap.m[parentDir]; ok {
				dirMap.RUnlock()
				parent.hasFile = true
				// 递归标记所有祖先目录
				for p := parent; p != nil; p = p.parent {
					p.hasFile = true
				}
			} else {
				dirMap.RUnlock()
			}
		}
		if !t.TaskConfig.ProcessEmptyDir {
			if info.IsDir {
				return nil
			}
		}

		file := FileInfo{Path: info.PathName, Size: info.Size}

		// 计数增加  外面快速统计已经有了  这里就不用加了
		//atomic.AddInt64(&t.ProgressInfo.TotalFiles, 1)
		//atomic.AddInt64(&t.ProgressInfo.TotalSize, file.Size)

		// 处理超大文件
		if file.Size > t.TaskConfig.MaxTaskSize {
			t.logger.Info("Found large file, creating direct task",
				zap.String("path", file.Path),
				zap.Int64("size", file.Size))

			newTask := NewTask()
			newTask.Files = append(newTask.Files[:0], file)
			newTask.TotalSize = file.Size
			t.taskChan <- newTask
			return nil
		}

		// 降低锁冲突
		shard := fileTask.taskShards[getShardIndex(file.Path)]
		shard.Lock()
		defer shard.Unlock()

		currentTask := shard.currentTask
		needNewTask := currentTask.TotalSize+file.Size > t.TaskConfig.MaxTaskSize ||
			len(currentTask.Files) >= t.TaskConfig.MaxFilesPerTask

		if !needNewTask {
			currentTask.Files = append(currentTask.Files, file)
			currentTask.TotalSize += file.Size
			return nil
		}

		// 提交任务并重置
		if len(currentTask.Files) > 0 {
			t.taskScanWg.Add(1)
			t.submitTask(currentTask) // 异步提交
			shard.currentTask = NewTask()
		}

		shard.currentTask.Files = append(shard.currentTask.Files, file)
		shard.currentTask.TotalSize += file.Size
		return nil
	}

	walker, err := t.getWorker()
	if err != nil {
		t.logger.Error("Error creating walker", zap.Error(err))
		return
	}

	t.logger.Info("start file scan",
		zap.String("sourceDir", t.SourceDir),
		zap.Any("task info ", t))

	// 取消计数扫描
	ctx, cancel := context.WithCancel(context.TODO())
	defer cancel()
	// 快速计算文件数量和文件大小
	go func() {
		t.logger.Info("start file scan")
		var fileCount int64
		var fileSize int64

		_ = walker.Walk(ctx, t.SourceDir, func(err error, info walk.FileInfo) error {
			if err != nil {
				return err
			}
			if info.IsDir {
				return nil
			}
			atomic.AddInt64(&t.ProgressInfo.TotalFiles, 1)
			atomic.AddInt64(&t.ProgressInfo.TotalSize, info.Size)
			return nil
		})

		t.logger.Info("file scan finished", zap.Int64("fileCount", fileCount), zap.Int64("fileSize", fileSize))
		//t.ProgressInfo.TotalSize = fileSize
		//t.ProgressInfo.TotalFiles = fileCount
	}()

	if err := walker.Walk(context.TODO(), t.SourceDir, walkFn); err != nil {
		t.logger.Error("FastWalk Error scanning files",
			zap.String("sourceDir", t.SourceDir),
			zap.Error(err))
	}
	defer func() {
		err = walker.Close()
		if err != nil {
			t.logger.Error("Error closing walker", zap.Error(err))
		}
	}()

	t.logger.Info("start file scan",
		zap.String("sourceDir", t.SourceDir),
		zap.Any("task", t))

	// 确保所有剩余文件都能生成任务
	for _, shard := range fileTask.taskShards {
		shard.Lock()
		if len(shard.currentTask.Files) > 0 {
			t.taskScanWg.Add(1)
			t.submitTask(shard.currentTask)
			shard.currentTask = NewTask()
		}
		shard.Unlock()
	}

	if t.TaskConfig.ProcessEmptyDir {
		// 处理空目录
		dirMap.RLock()
		nodes := make([]*dirNode, 0, len(dirMap.m))
		for _, node := range dirMap.m {
			nodes = append(nodes, node)
		}
		dirMap.RUnlock()
		t.logger.Info("Processing empty directories")
		for _, node := range nodes {
			if !node.hasFile && !node.processed {
				newTask := NewTask()
				newTask.IsDir = true
				newTask.DirPath = node.path
				t.taskChan <- newTask
				node.processed = true
				atomic.AddInt64(&t.ProgressInfo.TotalFiles, 1) // 将空目录计入总文件数
			}
		}
	}
	t.taskScanWg.Wait()
	// 确保所有 worker 都能正常退出
	close(t.taskChan)
	t.logger.Info("end file scan")
}

func (t *MigrateTask) getWorker() (walker walk.Walker, err error) {
	// 走ssh协议的远程文件系统
	if t.RemoteConfig.RemoteIp != "" && t.RemoteConfig.RemotePort != "" {

		pool, err := utils.NewSFTPConnectionPool(utils.NewClientConfig(t.RemoteConfig.RemoterUserName, t.RemoteConfig.RemotePassword),
			fmt.Sprintf("%s:%s", t.RemoteConfig.RemoteIp, t.RemoteConfig.RemotePort), runtime.NumCPU())
		if err != nil {
			t.logger.Error("Error creating SFTP connection pool", zap.Error(err))
			return nil, err
		}
		walker = walk.NewSFTPWalker(walk.WalkerConfig{
			Concurrency: 5 * runtime.NumCPU(),
			SrcDir:      t.SourceDir,
		}, pool, t.logger)
	} else {
		// 本地文件系统挂载
		walker = walk.NewWalker(walk.WalkerConfig{
			Concurrency: 5 * runtime.NumCPU(),
			SrcDir:      t.SourceDir,
		}, t.logger)
	}
	return walker, nil
}

func (t *MigrateTask) submitTask(task *Task) {
	defer t.taskScanWg.Done()
	t.taskChan <- task
	t.logger.Info("Submitting file task",
		zap.Int("fileCount", len(task.Files)),
		zap.Int64("totalSize", task.TotalSize))
}

func (t *MigrateTask) processFile() {
	t.logger.Info("start process fileList")
	localFileCollector := &LocalFileCollector{}
	file, err := localFileCollector.GetFile(t.FromFileList)
	if err != nil {
		t.logger.Error("Error opening file list")
		return
	}
	defer file.Close()

	// 创建一个 Scanner 来逐行读取文件
	scanner := bufio.NewScanner(file)
	// 检查是否有错误发生
	if err := scanner.Err(); err != nil {
		fmt.Printf("Error while reading file: %v\n", err)
	}

	newTask := NewTask()
	count := 0

	client := &utils.SFTPClient{}
	isRemote := len(strings.TrimSpace(t.RemoteConfig.RemoteIp)) > 0 && len(strings.TrimSpace(t.RemoteConfig.RemotePort)) > 0
	if isRemote {
		client, err = utils.NewSFTPClientByUserPass(t.RemoteConfig.RemoterUserName, t.RemoteConfig.RemotePassword, t.RemoteConfig.RemoteIp, t.RemoteConfig.RemotePort)
		if err != nil {
			t.logger.Error("Error while creating SFTP client", zap.Error(err))
			return
		}
		defer client.Close()
	}

	// 逐行读取
	for scanner.Scan() {
		line := scanner.Text() // 获取当前行的内容
		// idc 时 SourceDir 为空 ，line 为全路径， 不做处理
		// 阿里云 时 SourceDir 为自动生成的localPath , line 为cfs相对路径，需要拼接
		newLine := path.Join(t.SourceDir, line)

		param := strings.Split(newLine, "|")
		// 文件全路径 +大小信息
		if len(param) == 2 {
			size, _ := strconv.ParseInt(param[1], 10, 64)
			newTask.Files = append(newTask.Files, FileInfo{Path: param[0], Size: size})
			count++
		}
		if len(param) == 1 {
			var stat os.FileInfo
			var err_ error
			if isRemote {
				stat, err_ = client.Stat(param[0])
			} else {
				stat, err_ = os.Stat(param[0])
			}
			if err_ != nil {
				newTask.Files = append(newTask.Files, FileInfo{Path: param[0], Size: 0})
				count++
				continue
			}
			newTask.Files = append(newTask.Files, FileInfo{Path: param[0], Size: stat.Size()})
			newTask.TotalSize += stat.Size()
			count++
		}
		if len(param) != 2 && len(param) != 1 {
			t.logger.Error("Error parsing file list")
			continue
		}
		// 字符转数字
		if count >= t.TaskConfig.MaxFilesPerTask {
			t.taskChan <- newTask
			newTask = NewTask()
			count = 0
		}
	}
	if len(newTask.Files) > 0 {
		t.taskChan <- newTask
	}
	// 确保所有 worker 都能正常退出
	close(t.taskChan)
	t.logger.Info("end process fileList")
	return
}

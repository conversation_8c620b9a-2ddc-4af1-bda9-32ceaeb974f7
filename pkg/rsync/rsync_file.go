package rsync

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"os"
	"os/exec"
	"path/filepath"
	"runtime"
	"strings"
	"sync"
	"sync/atomic"
	"time"

	"git.woa.com/msp/toolbox/cfs-migrate-tool/pkg/logger"

	"git.woa.com/msp/toolbox/cfs-migrate-tool/pkg/walk"

	"git.woa.com/msp/toolbox/cfs-migrate-tool/pkg/common"
	"git.woa.com/msp/toolbox/cfs-migrate-tool/pkg/utils"

	_ "net/http/pprof"

	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
)

const (
	KB = 1024      // 1 KB
	MB = 1024 * KB // 1 MB
	GB = 1024 * MB // 1 GB
)

type MigrateConfig struct {
	SrcPath string
	DstPath string

	Threads          int
	ConfigFile       string
	Verbose          bool
	EnablePprof      bool
	FromFile         string
	ForceOverwrite   bool
	OverwriteByMtime bool
	RemoteConfig     RemoteConfig
}

type RemoteConfig struct {
	RemoteIp        string
	RemotePort      string
	RemoterUserName string
	RemotePassword  string
}

type MigrateTask struct {
	SourceDir    string       // 源目录
	DestDir      string       // 目标目录
	ScanTime     time.Time    // 开始扫描时间
	ProgressInfo ProgressInfo // 迁移进度信息
	logger       *zap.Logger
	taskChan     chan *Task
	taskScanWg   sync.WaitGroup   // 扫描任务同步等待
	errChan      chan ErrFileInfo // 错误文件处理记录
	taskLock     sync.Locker
	FromFileList string      // 可省略  当用户指定从指定文件列表迁移时，有值。为用户宿主机能访问的网络http:// url  或者是宿主机的绝对路径
	RsyncConfig  RsyncConfig // rsync 启动配置
	TaskConfig   TaskConfig  // 任务配置
	MigrateDone  bool
	TotalSize    int64
	RemoteConfig RemoteConfig
	rateLimiter  *time.Ticker // 限流器，3秒一个令牌
}

type TaskConfig struct {
	MaxTaskSize      int64         // 单个任务最大容量， 单位 B
	MaxFilesPerTask  int           // 单个任务最大文件数
	ProgressInterval time.Duration // 进度打印间隔
	ProcessEmptyDir  bool          // 是否处理空目录(默认不处理)
}

// RsyncConfig rsync 选项
type RsyncConfig struct {
	ForceOverwrite   bool // 强制覆盖
	OverwriteByMtime bool // 按照修改时间覆盖
}

func (t *MigrateTask) GetProgress() common.ProgressInfo {

	var sizeProgress float64
	if t.ProgressInfo.TotalSize != 0 {
		sizeProgress = float64(t.ProgressInfo.MigratedSize) / float64(t.ProgressInfo.TotalSize)
	}

	return common.ProgressInfo{
		// 没有好的统计总数量的方式
		//FileProgress:  sizeProgress,
		SizeProgress:    sizeProgress,
		TotalFiles:      t.ProgressInfo.TotalFiles,
		TotalSize:       t.ProgressInfo.TotalSize,
		MigratedFiles:   t.ProgressInfo.MigratedFiles,
		MigratedSize:    t.ProgressInfo.MigratedSize,
		MigrateDone:     t.MigrateDone,
		SendTrafficRate: t.ProgressInfo.SendTrafficRate,
		RecvTrafficRate: t.ProgressInfo.RecvTrafficRate,
	}
}

func NewMigrateTask(src, dest string, enablePprof bool, fromFileList string, rsync RsyncConfig, config RemoteConfig) *MigrateTask {

	if enablePprof {
		// 启动 pprof HTTP 服务器
		go func() {
			http.ListenAndServe("0.0.0.0:6060", nil)
		}()
	}
	if err := os.MkdirAll("logs", os.ModePerm); err != nil {
		panic(fmt.Sprintf("failed to create logs directory: %v", err))
	}

	cfg := zap.NewProductionConfig()
	cfg.OutputPaths = []string{"logs/migrate.log"}
	cfg.EncoderConfig.EncodeTime = func(t time.Time, enc zapcore.PrimitiveArrayEncoder) {
		enc.AppendString(t.Format("2006-01-02 15:04:05.000"))
	}
	logger, err := cfg.Build()
	if err != nil {
		panic(fmt.Sprintf("failed to initialize logger: %v", err))
	}

	task := &MigrateTask{
		logger:     logger,
		SourceDir:  src,
		DestDir:    dest,
		ScanTime:   time.Now(), // 开始任务时间
		taskScanWg: sync.WaitGroup{},
		taskChan:   make(chan *Task, 100),
		errChan:    make(chan ErrFileInfo, 100),
		TaskConfig: TaskConfig{
			MaxTaskSize:      1 * GB,
			MaxFilesPerTask:  5 * 1000,
			ProgressInterval: 5 * time.Second,
			ProcessEmptyDir:  false, // 默认不处理
		},
		taskLock:     &sync.Mutex{},
		FromFileList: fromFileList,
		RsyncConfig:  rsync,
		RemoteConfig: config,
		rateLimiter:  time.NewTicker(500 * time.Millisecond), // 初始化限流器，3秒一个令牌
	}

	return task
}

func (t *MigrateTask) Run() error {

	// 参数校验 ， 工单
	if t.RemoteConfig.RemoteIp != "" && t.RemoteConfig.RemotePort != "" && t.RemoteConfig.RemoterUserName != "" {

		err := utils.ValidateRemoteConfig(t.RemoteConfig.RemoteIp, t.RemoteConfig.RemoterUserName, t.RemoteConfig.RemotePassword, t.RemoteConfig.RemotePort)
		if err != nil {
			t.logger.Error("failed to validate remote config", zap.Error(err))
			return err
		}
	}
	// 初始化并发控制
	var initialConcurrency int = runtime.NumCPU() * 2
	sem := make(chan struct{}, initialConcurrency)
	// 初始化两倍CPU并发许可
	for i := 0; i < initialConcurrency; i++ {
		sem <- struct{}{}
	}

	// 使用context管理goroutine
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// 启动 CPU/内存 监控
	go t.monitorResources(ctx, sem)

	// 启动任务生成 goroutines
	startTime := time.Now()
	go t.generateTasks()

	// 启动进度打印 Goroutine
	go t.printProgress(ctx)

	t.logger.Info("Migration started", zap.Int("concurrency", initialConcurrency))
	// 启动多个 worker 处理任务
	var wg sync.WaitGroup
	for i := 0; i < initialConcurrency; i++ {
		wg.Add(1)
		go func(workerID int) {
			defer wg.Done()
			for task := range t.taskChan {
				// 获取并发许可

				<-sem
				// 处理任务
				if err := t.processTask(task); err != nil {
					t.logger.Error("Worker error",
						zap.Int("workerID", workerID),
						zap.Error(err))
				}
			}
		}(i)
	}
	wg.Wait()

	t.logger.Info("Migration completed",
		zap.Duration("totalTime", time.Since(startTime)))
	t.MigrateDone = true
	return nil
}

func (t *MigrateTask) processTask(task *Task) (err error) {
	startTime := time.Now()
	defer func() {
		if r := recover(); r != nil {
			err = fmt.Errorf("panic recovered: %v", r)
			t.logger.Error("Task panic",
				zap.Any("recover", r),
				zap.Stack("stack"))
		}

		duration := time.Since(startTime)
		if task.IsDir {
			// 更新目录任务统计
			atomic.AddInt64(&t.ProgressInfo.MigratedFiles, 1)
			if err != nil {
				t.logger.Error("Directory task failed",
					zap.String("path", task.DirPath),
					zap.Duration("duration", duration),
					zap.Error(err))
			} else {
				t.logger.Debug("Directory task completed",
					zap.String("path", task.DirPath),
					zap.Duration("duration", duration))
			}
			return
		}

		if err != nil {
			t.logger.Error("Task failed",
				zap.Int("fileCount", len(task.Files)),
				zap.Int64("totalSize", task.TotalSize),
				zap.Duration("duration", duration),
				zap.Error(err))
		} else {
			t.logger.Debug("Task completed",
				zap.Int("fileCount", len(task.Files)),
				zap.Int64("totalSize", task.TotalSize),
				zap.Duration("duration", duration))
		}
	}()

	// 处理目录任务
	if task.IsDir {
		relPath, err := filepath.Rel(t.SourceDir, task.DirPath)
		if err != nil {
			t.logger.Error("Error getting relative path",
				zap.String("path", task.DirPath),
				zap.Error(err))
			return err
		}
		destPath := filepath.Join(t.DestDir, relPath)
		if err := os.MkdirAll(destPath, os.ModePerm); err != nil {
			t.logger.Error("Error creating directory",
				zap.String("path", destPath),
				zap.Error(err))
			return err
		}

		// 同步目录元数据
		srcInfo, err := os.Stat(task.DirPath)
		if err != nil {
			t.logger.Error("Error getting source directory info",
				zap.String("path", task.DirPath),
				zap.Error(err))
			return err
		}

		// 同步权限
		if err := os.Chmod(destPath, srcInfo.Mode()); err != nil {
			t.logger.Error("Error setting directory permissions",
				zap.String("path", destPath),
				zap.Error(err))
			return err
		}

		// 同步修改时间
		if err := os.Chtimes(destPath, srcInfo.ModTime(), srcInfo.ModTime()); err != nil {
			t.logger.Error("Error setting directory times",
				zap.String("path", destPath),
				zap.Error(err))
			return err
		}
		return nil
	}

	// 处理文件任务
	// 创建临时文件
	t.logger.Info("Processing task",
		zap.Int("fileCount", len(task.Files)),
		zap.Int64("totalSize", task.TotalSize))
	tmpFile, err := os.CreateTemp("", "rsync_files_*.txt")
	if err != nil {
		return err
	}
	defer os.Remove(tmpFile.Name())

	// 写入文件路径
	for _, file := range task.Files {
		if _, err := tmpFile.WriteString(strings.TrimPrefix(file.Path, t.SourceDir) + "\n"); err != nil {
			return err
		}
	}
	tmpFile.Close()

	// 创建重试机制
	// TOOD 这里可以换成成熟的库，比如 github.com/avast/retry-go
	maxRetries := 3
	retryDelay := 5 * time.Second
	var lastErr error

	if t.RemoteConfig.RemoteIp != "" && t.RemoteConfig.RemotePort != "" && t.RemoteConfig.RemoterUserName != "" {
		lastErr = t.doMigrateWithRetry(task, maxRetries, tmpFile, lastErr, retryDelay, walk.SSH)
	} else {
		lastErr = t.doMigrateWithRetry(task, maxRetries, tmpFile, lastErr, retryDelay, walk.Local)
	}
	if lastErr != nil {
		t.logger.Error("Rsync command failed after retries",
			zap.String("destDir", t.DestDir),
			zap.Error(lastErr))
		return lastErr
	}

	t.logger.Debug("Rsync completed successfully",
		zap.String("destDir", t.DestDir),
		zap.Int("fileCount", len(task.Files)))

	// 更新统计
	atomic.AddInt64(&t.ProgressInfo.MigratedFiles, int64(len(task.Files)))
	atomic.AddInt64(&t.ProgressInfo.MigratedSize, task.TotalSize)
	return nil
}

func (t *MigrateTask) doMigrateWithRetryCommon(task *Task, maxRetries int, cmd *exec.Cmd, lastErr error, retryDelay time.Duration) error {
	i := 0
	for ; i < maxRetries; i++ {
		// 每次重试都创建新的命令实例
		newCmd := exec.Command(cmd.Path, cmd.Args[1:]...)
		newCmd.Stdout = io.Discard
		newCmd.Stderr = io.MultiWriter(os.Stderr, logger.NewLogWriterProxy(t.logger))
		newCmd.Env = cmd.Env

		t.logger.Info("Executing rsync command",
			zap.String("command", newCmd.String()),
			zap.Int("fileCount", len(task.Files)),
			zap.Int("retry", i))

		if err := newCmd.Run(); err == nil {
			// Success
			lastErr = nil
			break
		} else {
			lastErr = err
			if lastErr.Error() == "exit status 23" {
				t.logger.Error("Rsync command failed ,err code return  23,but will not retry",
					zap.String("destDir", t.DestDir),
					zap.Error(err),
					zap.Int("retry", i),
					zap.Duration("delay", retryDelay))
				lastErr = nil
				break
			}

			t.logger.Error("Rsync command failed, will retry",
				zap.String("destDir", t.DestDir),
				zap.Error(err),
				zap.Int("retry", i),
				zap.Duration("delay", retryDelay))
			if i < maxRetries-1 {
				time.Sleep(time.Duration(i+1) * retryDelay)
			}
		}
	}
	if i == maxRetries {
		t.logger.Error("Rsync command real failed after retries",
			zap.String("destDir", t.DestDir),
			zap.Error(lastErr))
	}

	return lastErr
}

func (t *MigrateTask) doMigrateWithRetryLocal(task *Task, maxRetries int, tmpFile *os.File, lastErr error, retryDelay time.Duration) error {
	args := []string{
		"--files-from", tmpFile.Name(),
		"-az",
		"--partial",
		"--relative",
		"--inplace",
		"--info", "progress2,stats0,name0",
	}
	if t.RsyncConfig.ForceOverwrite {
		args = append(args, "--ignore-times")
	} else if t.RsyncConfig.OverwriteByMtime {
		args = append(args, "--update")
	}
	args = append(args,
		filepath.Clean(t.SourceDir)+"/",
		filepath.Clean(t.DestDir),
	)
	t.logger.Info("Execute the command", zap.Any("args", args))
	cmd := exec.Command("rsync", args...)
	cmd.Stdout = os.Stdout
	cmd.Stderr = os.Stderr

	return t.doMigrateWithRetryCommon(task, maxRetries, cmd, lastErr, retryDelay)
}

// doMigrateWithRetryRemote  需要考虑  源端的 ssh MaxSessions 限制  ，不配置默认是10
func (t *MigrateTask) doMigrateWithRetryRemote(task *Task, maxRetries int, tmpFile *os.File, lastErr error, retryDelay time.Duration) error {
	// 构建安全的命令参数
	var args []string

	// 添加rsync基本参数
	args = append(args,
		"--files-from", tmpFile.Name(),
		"-avz",
		"--partial",
		"--relative",
		"--inplace",
		"--info", "progress2,stats0,name0")

	// 添加可选参数
	if t.RsyncConfig.ForceOverwrite {
		args = append(args, "--ignore-times")
	} else if t.RsyncConfig.OverwriteByMtime {
		args = append(args, "--update")
	}
	// 添加源和目标路径
	if len(strings.TrimSpace(t.SourceDir)) == 0 {
		t.SourceDir = "/"
	}

	// 构建远程源路径
	remoteSource := fmt.Sprintf("%s@%s:%s",
		t.RemoteConfig.RemoterUserName,
		t.RemoteConfig.RemoteIp,
		filepath.Clean(t.SourceDir))

	args = append(args, remoteSource, filepath.Clean(t.DestDir))

	// 记录命令（
	t.logger.Info("Execute rsync command", zap.Strings("args", args))

	// 等待限流器令牌
	<-t.rateLimiter.C

	// 创建环境变量
	env := append(os.Environ(), "SSHPASS="+t.RemoteConfig.RemotePassword)
	env = append(env, "RSYNC_RSH="+fmt.Sprintf("ssh -p %s -o StrictHostKeyChecking=no", t.RemoteConfig.RemotePort))
	// 构建完整的命令参数
	var cmdArgs []string
	cmdArgs = append(cmdArgs, "-e")    // sshpass 参数：使用环境变量中的密码
	cmdArgs = append(cmdArgs, "rsync") // rsync 命令本身
	cmdArgs = append(cmdArgs, args...) // 所有 rsync 参数

	// 创建命令
	cmd := exec.Command("sshpass", cmdArgs...)

	cmd.Env = env
	cmd.Stdout = os.Stdout
	cmd.Stderr = os.Stderr

	return t.doMigrateWithRetryCommon(task, maxRetries, cmd, lastErr, retryDelay)
}

// printProgress 进度打印
func (t *MigrateTask) printProgress(ctx context.Context) {
	ticker := time.NewTicker(t.TaskConfig.ProgressInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-ticker.C:
			netTraffic, err := utils.GetNetWorkTrafficSync("rsync")
			if err != nil {
				t.logger.Error("Get Network Traffic Failed", zap.Error(err))
				return
			}
			t.ProgressInfo.SendTrafficRate = netTraffic.RecvTrafficRate
			t.ProgressInfo.RecvTrafficRate = netTraffic.SendTrafficRate
		}
	}
}

func (t *MigrateTask) handleProgress(w http.ResponseWriter, r *http.Request) {
	progress := t.ProgressInfo
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(progress)
}

func (t *MigrateTask) doMigrateWithRetry(task *Task, maxRetries int, tmpFile *os.File, lastErr error, retryDelay time.Duration, _type string) error {
	if _type == walk.Local {
		return t.doMigrateWithRetryLocal(task, maxRetries, tmpFile, lastErr, retryDelay)
	}
	if _type == walk.SSH {
		return t.doMigrateWithRetryRemote(task, maxRetries, tmpFile, lastErr, retryDelay)
	}
	panic("not support type")
}

package utils

import (
	"bufio"
	"fmt"
	"log"
	"os"
)

// WriteStringsToFile 将字符串数组写入文件
func WriteStringsToFile(filename string, lines []string, prefix string) error {

	// 打开文件（如果文件不存在则创建，追加写模式）
	file, err := os.OpenFile(filename, os.O_APPEND|os.O_CREATE|os.O_WRONLY, 0644)
	if err != nil {
		return fmt.Errorf("failed to open file: %v", err)
	}
	defer func(file *os.File) {
		err := file.Close()
		if err != nil {
			log.Printf("failed to close file: %v", err)
		}
	}(file)

	// 创建缓冲写对象
	writer := bufio.NewWriter(file)

	// 写入字符串数组
	for _, line := range lines {
		// 写入缓冲区
		if _, err := writer.WriteString(line + "\n"); err != nil {
			return fmt.Errorf("failed to write to file: %v", err)
		}
	}

	// 刷新缓冲区，确保所有数据写入文件
	if err := writer.Flush(); err != nil {
		return fmt.Errorf("failed to flush buffer: %v", err)
	}
	return nil
}

func DeleteFile(filename string) {
	// 检查文件是否存在
	if _, err := os.Stat(filename); err == nil {
		// 如果文件存在，则删除文件
		err := os.Remove(filename)
		if err != nil {
			fmt.Println("删除旧日志文件失败:", err)
		}
	}

}

func TruncateFile(filename string) {
	// 打开文件并以截断模式清空内容

	// 检查文件是否存在
	if _, err := os.Stat(filename); err == nil {
		file, err := os.OpenFile(filename, os.O_WRONLY|os.O_TRUNC, 0644)
		if err != nil {
			log.Fatalf("Failed to open file: %v", err)
		}
		defer func(file *os.File) {
			err := file.Close()
			if err != nil {
				log.Fatalf("Failed to close file: %v", err)
			}
		}(file)
	}

}
